<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.mapper.basicData.DocumentInventoryDetailMapper">
    <!--
    性能优化建议：
    1. 在document_inventory_detail表上创建复合索引：(warehouse_status, create_time)
    2. 在document_inventory_detail表上创建索引：(detail_code, material_code, container_code)
    3. 在basic_document_detail表上创建索引：(document_code)
    4. 在basic_document_info表上创建索引：(transaction_code, business_type, transaction_type)
    -->
    <select id="queryDocumentInventoryDetail" resultType="com.ruoyi.vo.basicData.DocumentInventoryDetailDto">
        SELECT
            a.id,
            a.document_code,
            a.business_type,
            a.detail_code,
            a.inventory_id,
            a.batch_code,
            a.material_code,
            a.transaction_type,
            a.quantity,
            a.completed_num,
            a.is_concession,
            a.qc_status,
            a.warehouse_status,
            a.container_code,
            a.create_time,
            a.completed_time,
            c.transaction_code,
            mi.material_name,
            ci.company_name as supplierCustomerName,
            bwi.warehouse_name as warehouseName,
            bwl.node_name as positionName,
            level_loc.node_name as levelName,
            shelf_loc.node_name as shelfName
        FROM document_inventory_detail a
        LEFT JOIN basic_document_detail b ON a.detail_code = b.id
        LEFT JOIN basic_document_info c ON b.document_code = c.id
        LEFT JOIN basic_company_info ci ON c.supply_sales_code = ci.company_code
        LEFT JOIN basic_material_info mi ON a.material_code = mi.material_code
        LEFT JOIN basic_warehouse_container bwc ON a.container_code = bwc.container_code
        LEFT JOIN basic_warehouse_location bwl ON bwc.location_code = bwl.location_code
        LEFT JOIN basic_warehouse_location level_loc ON level_loc.id = bwl.parent_id
        LEFT JOIN basic_warehouse_location shelf_loc ON shelf_loc.id = level_loc.parent_id
        LEFT JOIN basic_warehouse_info bwi ON bwl.warehouse_code = bwi.warehouse_code
        WHERE 1 = 1

        <if test="keyWord != null and keyWord != ''">
            AND a.detail_code = #{keyWord}
        </if>
        <if test="keySubWord != null and keySubWord != ''">
            AND c.transaction_code LIKE CONCAT('%', #{keySubWord}, '%')
        </if>
        <if test="keyThirdWord != null and keyThirdWord != ''">
            AND c.business_type = #{keyThirdWord}
        </if>
        <if test="keyFourWord != null and keyFourWord != ''">
            AND c.transaction_type = #{keyFourWord}
        </if>
        <if test="state != null">
            AND a.warehouse_status = #{state}
        </if>
        <if test="stateSub != null">
            AND a.qc_status = #{stateSub}
        </if>
        <if test="warehouseName != null and warehouseName != ''">
            AND bwi.warehouse_name LIKE CONCAT('%', #{warehouseName}, '%')
        </if>
        <if test="supplierCustomerName != null and supplierCustomerName != ''">
            AND (ci.company_code LIKE CONCAT('%', #{supplierCustomerName}, '%')
                 OR ci.company_name LIKE CONCAT('%', #{supplierCustomerName}, '%'))
        </if>
        <if test="materialInfo != null and materialInfo != ''">
            AND (mi.material_code LIKE CONCAT('%', #{materialInfo}, '%')
                 OR mi.material_name LIKE CONCAT('%', #{materialInfo}, '%'))
        </if>
        <if test="batchCode != null and batchCode != ''">
            AND a.batch_code LIKE CONCAT('%', #{batchCode}, '%')
        </if>

        <!-- 创建时间范围查询 -->
        <if test="bdate != null and bdate != ''">
            AND a.create_time &gt;= #{bdate}
        </if>
        <if test="edate != null and edate != ''">
            AND a.create_time &lt;= #{edate}
        </if>

        <!-- 完成时间范围查询 -->
        <if test="completedStartDate != null and completedStartDate != ''">
            AND a.completed_time &gt;= #{completedStartDate}
        </if>
        <if test="completedEndDate != null and completedEndDate != ''">
            AND a.completed_time &lt;= #{completedEndDate}
        </if>

        ORDER BY
            a.warehouse_status ASC,  -- 未完成的状态优先显示
            CASE
                WHEN a.completed_time IS NOT NULL THEN a.completed_time
                ELSE a.create_time
            END DESC
    </select>

    <select id="selectByDetailCode" resultType="com.ruoyi.domain.basicData.DocumentInventoryDetail">
        SELECT *
        FROM document_inventory_detail
        WHERE detail_code = #{detailCode}
        ORDER BY id
    </select>
</mapper>

