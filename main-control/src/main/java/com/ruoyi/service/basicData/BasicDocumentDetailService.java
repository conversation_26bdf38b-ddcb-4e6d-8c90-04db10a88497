package com.ruoyi.service.basicData;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.domain.basicData.BasicDocumentDetail;
import com.ruoyi.domain.basicData.BasicDocumentInfo;
import com.ruoyi.mapper.basicData.BasicDocumentDetailMapper;
import com.ruoyi.mapper.basicData.BasicDocumentInfoMapper;
import com.ruoyi.service.document.DocumentDetailResponse;
import com.ruoyi.utils.QueryParamVO;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.webRequest.BatchIdsReq;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class BasicDocumentDetailService extends ServiceImpl<BasicDocumentDetailMapper, BasicDocumentDetail> {

    @Resource
    BasicDocumentDetailMapper basicDocumentDetailMapper;
    @Resource
    BasicDocumentInfoMapper basicDocumentInfoMapper;
    @Resource
    BasicMaterialBatchInventoryService basicMaterialBatchInventoryService;
    /**
     * 查询
     */
    public List<DocumentDetailResponse> queryDocumentDetail(QueryParamVO queryParamVO) {
        return basicDocumentDetailMapper.queryMesDocumentDetail(queryParamVO);
    }
    /**
     * 新增
     */
    public ResponseResult addDocumentDetail(BasicDocumentDetail param) {
        param.setId(UUID.randomUUID().toString());
        param.setCompletedNum(0);
        param.setTotalArrivalNum(0); // 初始化累计确认数量
        param.setContainerTotalConfirmed(0); // 初始化容器累计确认数量
        param.setTaskStatus(CommonConstant.TaskStatus.PENDING); // 初始任务状态为待处理
        basicDocumentDetailMapper.insert(param);
        return ResponseResult.getSuccessResult();
    }
    /**
     * 更新
     */
    public ResponseResult updateDocumentDetail(BasicDocumentDetail param) {
        BasicDocumentDetail basicDocumentDetail = basicDocumentDetailMapper.selectById(param.getId());
        BasicDocumentInfo basicDocumentInfo = basicDocumentInfoMapper.selectById(param.getId());
        if (basicDocumentInfo.getTransactionType() == CommonConstant.InoutType.OUT) {
            // 判断物料库存是否足够
            Integer num = basicMaterialBatchInventoryService.queryMaterialNumByCode(basicDocumentDetail.getMaterialCode());
            if (num < basicDocumentDetail.getQuantity()) {
                return ResponseResult.getErrorResult(basicDocumentDetail.getMaterialCode() + "物料库存数量为:" + num + "小于出库数量" +
                        basicDocumentDetail.getQuantity() + "请修改数量!");
            }
        }
        basicDocumentDetail.setQuantity(param.getQuantity());
        basicDocumentDetailMapper.updateById(basicDocumentDetail);
        return ResponseResult.getSuccessResult();
    }

    /**
     * 删除
     */
    public ResponseResult deleteDocumentDetail(BatchIdsReq req) {
        List<String> ids = req.getIds();
        for (String idTemp : ids){
            basicDocumentDetailMapper.deleteById(idTemp);
        }
        return ResponseResult.getSuccessResult();
    }
}
