package com.ruoyi.service.work;

import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.domain.basicData.*;
import com.ruoyi.mapper.basicData.BasicDocumentDetailMapper;
import com.ruoyi.mapper.basicData.BasicDocumentInfoMapper;
import com.ruoyi.mapper.basicData.BasicMaterialBatchInventoryMapper;
import com.ruoyi.mapper.basicData.BasicWarehouseContainerMapper;
import com.ruoyi.service.basicData.BasicDocumentInfoService;
import com.ruoyi.service.basicData.BasicMaterialBatchInventoryService;
import com.ruoyi.service.basicData.DocumentInventoryDetailService;
import com.ruoyi.service.erp.ErpReportService;
import com.ruoyi.utils.DateAndTimeUtil;
import com.ruoyi.utils.ResponseResult;
import com.ruoyi.utils.ResultMsg;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.document.DocumentDetailVo;
import com.ruoyi.vo.document.DocumentInventoryDetailVo;
import com.ruoyi.vo.document.DocumentInventoryVo;
import com.ruoyi.vo.lk.LkSendTaskDetail;
import com.ruoyi.vo.lk.LkSendTaskRequest;
import com.ruoyi.vo.lk.LkTaskMaterial;
import com.ruoyi.vo.mes.DocumentReportDetail;
import com.ruoyi.vo.mes.MesDocumentMaterial;
import com.ruoyi.vo.mes.MesDocumentReport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DocumentExecuteService {
    @Resource
    BasicDocumentInfoMapper basicDocumentInfoMapper;

    @Resource
    BasicDocumentDetailMapper basicDocumentDetailMapper;

    @Resource
    DocumentInventoryDetailService documentInventoryDetailService;

    @Resource
    BasicWarehouseContainerMapper basicWarehouseContainerMapper;

    @Resource
    BasicMaterialBatchInventoryMapper materialBatchInventoryMapper;

    @Resource
    BasicMaterialBatchInventoryService basicMaterialBatchInventoryService;

    @Resource
    LkSystemService lkSystemService;

    @Resource
    MesUpperService mesUpperService;

    @Resource
    RecordMaterialInoutService recordMaterialInoutService;

    @Resource
    ErpReportService erpReportService;

    @Resource
    BasicDocumentInfoService basicDocumentInfoService;


    private void executeDocumentStatus(List<DocumentDetailVo> lists) {
        for (DocumentDetailVo documentDetail : lists) {
            BasicDocumentInfo basicDocumentInfo = basicDocumentInfoMapper.getDocumentInfoByCode(documentDetail.getDocumentCode());
            //查询明细
            List<BasicDocumentDetail> basicDocumentDetails = basicDocumentDetailMapper.selectByDocumentCode(basicDocumentInfo.getId());
            Boolean flag = true;
            for (BasicDocumentDetail basicDocumentDetail : basicDocumentDetails) {
                if (basicDocumentDetail.getQuantity() != basicDocumentDetail.getCompletedNum()) {
                    flag = false;
                }
            }
            if (flag) {
                basicDocumentInfo.setStatus(CommonConstant.DocumentStatus.FINISH);
                basicDocumentInfoMapper.updateById(basicDocumentInfo);
            }
        }

    }

    /**
     * 单据出入库
     */
    @Transactional
    public ResponseResult documentInventoryInOut(DocumentInventoryVo param) {
        List<DocumentDetailVo> lists = param.getLists();
        Integer type = param.getType();

        // 验证单据数量逻辑
        ResponseResult validationResult = validateDocumentQuantities(lists);
        if (!validationResult.getCode().equals(ResultMsg.successCode)) {
            return validationResult;
        }
        //区分立库与平面库
        List<DocumentDetailVo> boxLk = new ArrayList<>();
        List<DocumentDetailVo> plateLk = new ArrayList<>();
        List<DocumentDetailVo> profileLk = new ArrayList<>();
        List<DocumentDetailVo> withoutLiKu = new ArrayList<>();
        //出入库list记录
        List<RecordMaterialInout> inOutList = new ArrayList<>();
        for (DocumentDetailVo documentDetail : lists) {
            List<DocumentInventoryDetailVo> details = documentDetail.getDetails();

            // 根据入库/出库类型获取正确的容器信息并分类
            List<DocumentInventoryDetailVo> boxLkDetails = new ArrayList<>();
            List<DocumentInventoryDetailVo> plateLkDetails = new ArrayList<>();
            List<DocumentInventoryDetailVo> profileLkDetails = new ArrayList<>();
            List<DocumentInventoryDetailVo> withoutLiKuDetails = new ArrayList<>();

            for (DocumentInventoryDetailVo detail : details) {
                String containerCode = getContainerCodeByType(detail, type);
                if (containerCode != null) {
                    if (containerCode.contains(CommonConstant.LkName.BOX)) {
                        boxLkDetails.add(detail);
                    } else if (containerCode.contains(CommonConstant.LkName.PLATE)) {
                        plateLkDetails.add(detail);
                    } else if (containerCode.contains(CommonConstant.LkName.PROFILE)) {
                        profileLkDetails.add(detail);
                    } else if (!containerCode.contains("立库")) {
                        withoutLiKuDetails.add(detail);
                    }
                }
            }
            //包含立库
            if (!boxLkDetails.isEmpty()) {
                documentDetail.setDetails(boxLkDetails);
                boxLk.add(documentDetail);
            }

            if (!plateLkDetails.isEmpty()) {
                documentDetail.setDetails(plateLkDetails);
                plateLk.add(documentDetail);
            }

            if (!profileLkDetails.isEmpty()) {
                documentDetail.setDetails(profileLkDetails);
                profileLk.add(documentDetail);
            }
            //不含立库
            if (!withoutLiKuDetails.isEmpty()) {
                documentDetail.setDetails(withoutLiKuDetails);
                withoutLiKu.add(documentDetail);
            }
            //增加出入库明细
            for (DocumentInventoryDetailVo documentInventoryDetailVo : details) {
                BasicDocumentInfo basicDocumentInfo = basicDocumentInfoMapper.getDocumentInfoByCode(documentDetail.getDocumentCode());
                RecordMaterialInout recordMaterialInout = new RecordMaterialInout();
                recordMaterialInout.setId(UUID.randomUUID().toString());
                recordMaterialInout.setInoutType(basicDocumentInfo.getTransactionType());
                recordMaterialInout.setDataOrigin(basicDocumentInfo.getBusinessSource());
                recordMaterialInout.setBoundType(basicDocumentInfo.getBusinessType());
                recordMaterialInout.setMaterialCode(documentDetail.getMaterialCode());
                recordMaterialInout.setTotalNum(documentInventoryDetailVo.getQuantity());
                recordMaterialInout.setBoundIndex(basicDocumentInfo.getTransactionCode());
                recordMaterialInout.setRecordDate(DateAndTimeUtil.getNowDate());
                recordMaterialInout.setContainerCode(documentInventoryDetailVo.getContainerCode());
                recordMaterialInout.setRecorder(SecurityUtils.getUsername());
                recordMaterialInout.setLockTime(DateAndTimeUtil.getNowDate());
                recordMaterialInout.setUpperIndex(basicDocumentInfo.getTransactionCode());
                inOutList.add(recordMaterialInout);
            }
        }
        //wms仓库
        this.executeWithoutLkTask(withoutLiKu);
        //立库
        if (!boxLk.isEmpty()) {
            this.executeLkTask(boxLk, CommonConstant.LkName.BOX, type);
        }
        if (!plateLk.isEmpty()) {
            this.executeLkTask(plateLk, CommonConstant.LkName.PLATE, type);
        }
        if (!profileLk.isEmpty()) {
            this.executeLkTask(profileLk, CommonConstant.LkName.PROFILE, type);
        }
        //处理单据状态
        this.executeDocumentStatus(lists);
        //增加出入库明细
        recordMaterialInoutService.addBatchInout(inOutList);
        //上报ERP（只有ERP来源的单据才上报，生产相关的除外）
        List<DocumentDetailVo> erpSourceLists = filterErpSourceDocuments(lists);
        if (!erpSourceLists.isEmpty()) {
            erpReportService.reportCurrentOperation(erpSourceLists, type);
        }
        return ResponseResult.getSuccessResult();
    }

    /**
     * 过滤出ERP来源的单据
     *
     * @param lists 单据明细列表
     * @return ERP来源的单据明细列表
     */
    private List<DocumentDetailVo> filterErpSourceDocuments(List<DocumentDetailVo> lists) {
        List<DocumentDetailVo> erpSourceLists = new ArrayList<>();

        for (DocumentDetailVo detail : lists) {
            try {
                BasicDocumentInfo documentInfo = basicDocumentInfoMapper.getDocumentInfoByCode(detail.getDocumentCode());
                if (documentInfo != null && CommonConstant.BusinessSource.ERP == documentInfo.getBusinessSource()) {
                    erpSourceLists.add(detail);
                }
            } catch (Exception e) {
                log.error("检查单据来源失败，单据编码：{}，错误：{}", detail.getDocumentCode(), e.getMessage(), e);
            }
        }

        return erpSourceLists;
    }

    /**
     * 立库数据处理
     */
    private ResponseResult executeLkTask(List<DocumentDetailVo> withLiKu, String lkName, Integer type) {
        LkSendTaskRequest lkSendTaskRequest = new LkSendTaskRequest();
        lkSendTaskRequest.setId(UUID.randomUUID().toString());
        lkSendTaskRequest.setTimestamp(new Date());
        List<LkSendTaskDetail> data = new ArrayList<>();
        for (DocumentDetailVo documentDetailVo : withLiKu) {
            LkSendTaskDetail lkSendTaskDetail = new LkSendTaskDetail();
            String taskNo = this.getLkTaskNo(documentDetailVo.getDocumentCode());
            lkSendTaskDetail.setTaskNo(taskNo);
            lkSendTaskDetail.setSource("WMS");
            List<LkTaskMaterial> materielInfos = new ArrayList<>();
            for (DocumentInventoryDetailVo documentInventoryDetailVo : documentDetailVo.getDetails()) {
                LkTaskMaterial lkTaskMaterial = new LkTaskMaterial();
                lkTaskMaterial.setMaterielCode(documentDetailVo.getMaterialCode());
                lkTaskMaterial.setQuantity(documentInventoryDetailVo.getQuantity());
                materielInfos.add(lkTaskMaterial);
            }
            lkSendTaskDetail.setMaterielInfos(materielInfos);
            data.add(lkSendTaskDetail);
        }
        lkSendTaskRequest.setData(data);
        if (type == CommonConstant.InoutType.IN) {
            ResponseResult responseResult = lkSystemService.sendLkInTask(lkSendTaskRequest, lkName);
            if (responseResult != null && responseResult.getCode().equals(ResultMsg.successCode)) {
                for (DocumentDetailVo documentDetailVo : withLiKu) {
                    BasicDocumentDetail basicDocumentDetail = basicDocumentDetailMapper.selectById(documentDetailVo.getId());
                    // 只更新已完成数量，不保存临时的 currentNum
                    basicDocumentDetail.setCompletedNum(basicDocumentDetail.getCompletedNum() + documentDetailVo.getCurrentNum());
                    basicDocumentDetailMapper.updateById(basicDocumentDetail);
                    List<DocumentInventoryDetailVo> details = documentDetailVo.getDetails();
                    for (DocumentInventoryDetailVo documentInventoryDetailVo : details) {
                        // 更新现有的DocumentInventoryDetail记录
                        DocumentInventoryDetail existingDetail = documentInventoryDetailService.getById(documentInventoryDetailVo.getId());
                        if (existingDetail != null) {
                            // 立库入库时需要更新容器信息（用户选择的目标容器）
                            if (type == CommonConstant.InoutType.IN) {
                                existingDetail.setContainerCode(documentInventoryDetailVo.getContainerCode());
                            }
                            existingDetail.setCompletedNum(existingDetail.getCompletedNum() + documentInventoryDetailVo.getQuantity());
                            // 检查批次是否完全完成，如果是则更新状态为已完成
                            if (existingDetail.getCompletedNum().equals(existingDetail.getQuantity())) {
                                existingDetail.setWarehouseStatus(CommonConstant.WarehouseStatus.COMPLETED);
                                existingDetail.setCompletedTime(new Date());
                            }
                            documentInventoryDetailService.updateById(existingDetail);
                        }
                    }
                }
            }
            return responseResult;
        } else {
            ResponseResult responseResult = lkSystemService.sendLkOutTask(lkSendTaskRequest, lkName);
            if (responseResult != null && responseResult.getCode().equals(ResultMsg.successCode)) {
                for (DocumentDetailVo documentDetailVo : withLiKu) {
                    BasicDocumentDetail basicDocumentDetail = basicDocumentDetailMapper.selectById(documentDetailVo.getId());
                    // 只更新已完成数量，不保存临时的 currentNum
                    basicDocumentDetail.setCompletedNum(basicDocumentDetail.getCompletedNum() + documentDetailVo.getCurrentNum());
                    basicDocumentDetailMapper.updateById(basicDocumentDetail);
                    List<DocumentInventoryDetailVo> details = documentDetailVo.getDetails();
                    for (DocumentInventoryDetailVo documentInventoryDetailVo : details) {
                        DocumentInventoryDetail documentInventoryDetail = documentInventoryDetailService.getById(documentInventoryDetailVo.getId());
                        documentInventoryDetail.setCompletedNum(documentInventoryDetail.getCompletedNum() + documentInventoryDetailVo.getQuantity());

                        // 检查批次是否完全完成，如果是则更新状态为已完成
                        if (documentInventoryDetail.getCompletedNum().equals(documentInventoryDetail.getQuantity())) {
                            documentInventoryDetail.setWarehouseStatus(CommonConstant.WarehouseStatus.COMPLETED);
                            documentInventoryDetail.setCompletedTime(new Date());
                        }

                        documentInventoryDetailService.updateById(documentInventoryDetail);
                    }

                    // 更新最新批次状态
                    basicDocumentInfoService.updateLatestBatchStatus(documentDetailVo.getId());
                }
            }
            return responseResult;
        }
    }

    public String getLkTaskNo(String baseCode) {
        // 1. 获取当前时间戳（精确到毫秒）
        String timestamp = String.valueOf(System.currentTimeMillis());
        // 3. 组合生成任务号
        return String.format("%s-%s", baseCode, timestamp);
    }

    /**
     * 非立库数据处理
     */
    private ResponseResult executeWithoutLkTask(List<DocumentDetailVo> withLiKu) {
        MesDocumentReport mesDocumentReport = new MesDocumentReport();
        List<DocumentReportDetail> documents = new ArrayList<>();
        for (DocumentDetailVo documentDetailVo : withLiKu) {
            DocumentReportDetail documentReportDetail = new DocumentReportDetail();
            BasicDocumentInfo basicDocumentInfo = basicDocumentInfoMapper.getDocumentInfoByCode(documentDetailVo.getDocumentCode());
            //保存报工数据
            documentReportDetail.setTransactionCode(basicDocumentInfo.getTransactionCode());
            documentReportDetail.setBusinessType(basicDocumentInfo.getBusinessType());
            List<MesDocumentMaterial> materialDetails = new ArrayList<>();

            List<DocumentInventoryDetailVo> details = documentDetailVo.getDetails();
            for (DocumentInventoryDetailVo documentInventoryDetailVo : details) {
                //单据入库详情新增
                if (basicDocumentInfo.getTransactionType() == CommonConstant.InoutType.IN) {
                    // 入库：使用前端传递的容器编码（用户选择的目标容器）
                    String targetContainerCode = documentInventoryDetailVo.getContainerCode();
                    BasicWarehouseContainer basicWarehouseContainer = basicWarehouseContainerMapper.getContainerByCode(targetContainerCode);
                    if (basicWarehouseContainer != null) {
                        //容器库位,新增批次
                        BasicMaterialBatchInventory basicMaterialBatchInventory = new BasicMaterialBatchInventory();
                        basicMaterialBatchInventory.setId(UUID.randomUUID().toString());
                        basicMaterialBatchInventory.setBatch("默认批次");
                        basicMaterialBatchInventory.setMaterialCode(documentDetailVo.getMaterialCode());
                        basicMaterialBatchInventory.setAvailNum(documentInventoryDetailVo.getQuantity());
                        basicMaterialBatchInventory.setContainerCode(targetContainerCode);
                        basicMaterialBatchInventory.setFreezeNum(0);
                        basicMaterialBatchInventory.setCreateTime(new Date());
                        basicMaterialBatchInventory.setProduceDate(new Date());
                        basicMaterialBatchInventory.setInDate(new Date());
                        basicMaterialBatchInventory.setUpperIndex(documentDetailVo.getDocumentCode());
                        basicMaterialBatchInventory.setMaterialNum(documentInventoryDetailVo.getQuantity());
                        materialBatchInventoryMapper.insert(basicMaterialBatchInventory);

                        // 更新现有的DocumentInventoryDetail记录
                        DocumentInventoryDetail existingDetail = documentInventoryDetailService.getById(documentInventoryDetailVo.getId());
                        if (existingDetail != null) {
                            // 入库时需要更新容器信息（用户选择的目标容器）
                            existingDetail.setContainerCode(targetContainerCode);
                            existingDetail.setCompletedNum(existingDetail.getCompletedNum() + documentInventoryDetailVo.getQuantity());
                            // 检查批次是否完全完成，如果是则更新状态为已完成
                            if (existingDetail.getCompletedNum().equals(existingDetail.getQuantity())) {
                                existingDetail.setWarehouseStatus(CommonConstant.WarehouseStatus.COMPLETED);
                                existingDetail.setCompletedTime(new Date());
                            }
                            documentInventoryDetailService.updateById(existingDetail);
                        }
                    }
                }
                //单据出库详情
                if (basicDocumentInfo.getTransactionType() == CommonConstant.InoutType.OUT) {
                    DocumentInventoryDetail documentInventoryDetail = documentInventoryDetailService.getById(documentInventoryDetailVo.getId());
                    // 通过容器编码和物料编码查询批次库存
                    BasicMaterialBatchInventory basicMaterialBatchInventory = basicMaterialBatchInventoryService.getInventoryByContainerAndMaterial(
                            documentInventoryDetail.getContainerCode(), documentInventoryDetail.getMaterialCode());
                    if (basicMaterialBatchInventory != null) {
                        basicMaterialBatchInventory.setFreezeNum(basicMaterialBatchInventory.getFreezeNum() - documentInventoryDetailVo.getQuantity());
                        basicMaterialBatchInventory.setMaterialNum(basicMaterialBatchInventory.getMaterialNum() - documentInventoryDetailVo.getQuantity());
                        basicMaterialBatchInventory.setAvailNum(basicMaterialBatchInventory.getMaterialNum() - basicMaterialBatchInventory.getFreezeNum());
                        materialBatchInventoryMapper.updateById(basicMaterialBatchInventory);
                        //库存数量为0删除该批次库存
                        if (basicMaterialBatchInventory.getMaterialNum() == 0 && basicMaterialBatchInventory.getAvailNum() == 0) {
                            materialBatchInventoryMapper.deleteById(basicMaterialBatchInventory.getId());
                        }
                    }
                    // 更新 document_inventory_detail 表的已完成数量
                    if (documentInventoryDetail.getCompletedNum() == null) {
                        documentInventoryDetail.setCompletedNum(0);
                    }
                    documentInventoryDetail.setCompletedNum(documentInventoryDetail.getCompletedNum() + documentInventoryDetailVo.getQuantity());

                    // 检查批次是否完全完成，如果是则更新状态为已完成
                    if (documentInventoryDetail.getCompletedNum().equals(documentInventoryDetail.getQuantity())) {
                        documentInventoryDetail.setWarehouseStatus(CommonConstant.WarehouseStatus.COMPLETED);
                        documentInventoryDetail.setCompletedTime(new Date());
                    }

                    documentInventoryDetailService.updateById(documentInventoryDetail);
                }
            }
            BasicDocumentDetail basicDocumentDetail = basicDocumentDetailMapper.selectById(documentDetailVo.getId());
            // 只更新已完成数量，不保存临时的 currentNum
            basicDocumentDetail.setCompletedNum(basicDocumentDetail.getCompletedNum() + documentDetailVo.getCurrentNum());
            basicDocumentDetailMapper.updateById(basicDocumentDetail);
            // 更新最新批次状态
            basicDocumentInfoService.updateLatestBatchStatus(documentDetailVo.getId());
            //报工明细保存
            MesDocumentMaterial mesDocumentMaterial = new MesDocumentMaterial();
            mesDocumentMaterial.setMaterialCode(documentDetailVo.getMaterialCode());
            mesDocumentMaterial.setQuantity(documentDetailVo.getCurrentNum());
            materialDetails.add(mesDocumentMaterial);
            documentReportDetail.setMaterialDetails(materialDetails);
            documents.add(documentReportDetail);
        }
        mesDocumentReport.setDocuments(documents);
        //报工MES
//        mesUpperService.reportDocument(mesDocumentReport);
        return ResponseResult.getSuccessResult();
    }

    /**
     * 验证单据数量逻辑
     * @param lists 单据明细列表
     * @return 验证结果
     */
    private ResponseResult validateDocumentQuantities(List<DocumentDetailVo> lists) {
        for (DocumentDetailVo documentDetail : lists) {
            // 查询单据明细信息
            BasicDocumentDetail basicDocumentDetail = basicDocumentDetailMapper.selectById(documentDetail.getId());
            if (basicDocumentDetail == null) {
                return ResponseResult.getErrorResult("单据明细不存在，ID: " + documentDetail.getId());
            }

            Integer currentNum = documentDetail.getCurrentNum();
            Integer quantity = basicDocumentDetail.getQuantity();
            Integer completedNum = basicDocumentDetail.getCompletedNum();

            // 验证本次数量必须大于0
            if (currentNum == null || currentNum <= 0) {
                return ResponseResult.getErrorResult("物料编码 " + documentDetail.getMaterialCode() +
                    " 的本次数量必须大于0，当前值: " + currentNum);
            }

            // 计算剩余可操作数量
            Integer remainingQuantity = quantity - completedNum;

            // 验证本次数量不能超过剩余可操作数量
            if (currentNum > remainingQuantity) {
                return ResponseResult.getErrorResult("物料编码 " + documentDetail.getMaterialCode() +
                    " 的本次数量 " + currentNum + " 超过了剩余可操作数量 " + remainingQuantity +
                    "（计划数量: " + quantity + "，已完成数量: " + completedNum + "）");
            }
        }

        return ResponseResult.getSuccessResult();
    }

    /**
     * 根据入库/出库类型获取正确的容器编码
     * 入库：使用前端传递的容器编码（用户选择的目标容器）
     * 出库：从数据库记录中获取容器编码（已确定的源容器）
     */
    private String getContainerCodeByType(DocumentInventoryDetailVo detailVo, Integer type) {
        if (type == CommonConstant.InoutType.IN) {
            // 入库：使用前端传递的容器编码
            return detailVo.getContainerCode();
        } else {
            // 出库：从数据库记录中获取容器编码
            DocumentInventoryDetail existingDetail = documentInventoryDetailService.getById(detailVo.getId());
            return existingDetail != null ? existingDetail.getContainerCode() : null;
        }
    }
}
}